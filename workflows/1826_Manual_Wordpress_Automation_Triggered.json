{"id": "caaf1WFANPKAikiH", "meta": {"instanceId": "558d88703fb65b2d0e44613bc35916258b0f0bf983c5d4730c00c424b77ca36a", "templateCredsSetupCompleted": true}, "name": "Auto categorize wordpress template", "tags": [], "nodes": [{"id": "2017403c-7496-48f8-a487-8a017c7adfe3", "name": "When clicking ‘Test workflow’", "type": "n8n-nodes-base.manualTrigger", "position": [680, 320], "parameters": {}, "typeVersion": 1}, {"id": "82ff288f-4234-4192-9046-33e5ffee5264", "name": "Wordpress", "type": "n8n-nodes-base.wordpress", "position": [1500, 320], "parameters": {"postId": "={{ $('Get All Wordpress Posts').item.json.id }}", "operation": "update", "updateFields": {"categories": "={{ $json.output }}"}}, "credentials": {"wordpressApi": {"id": "lGWPwxTdfPDDbFjj", "name": "Rumjahn.com wordpress"}}, "typeVersion": 1}, {"id": "521deb22-62dd-4b5f-8b9a-aab9777821da", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [620, -100], "parameters": {"width": 504.88636363636317, "content": "## How to Auto-Categorize 82 Blog Posts in 2 Minutes using <PERSON>.I. (No Coding Required)\n\n💡 Read the [case study here](https://rumjahn.com/how-to-use-a-i-to-categorize-wordpress-posts-and-streamline-your-content-organization-process/).\n\n📺 Watch the [youtube tutorial here](https://www.youtube.com/watch?v=IvQioioVqhw)\n\n"}, "typeVersion": 1}, {"id": "4090d827-f8cd-47ef-ad4f-654ee58216f6", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [860, 180], "parameters": {"color": 3, "width": 188.14814814814804, "height": 327.3400673400663, "content": "### Get wordpress posts\n\nTurn off return all if you're running into issues.\n"}, "typeVersion": 1}, {"id": "71585d54-fdcc-42a5-8a0e-0fac3adc1809", "name": "Sticky Note2", "type": "n8n-nodes-base.stickyNote", "position": [1080, 80], "parameters": {"color": 4, "width": 315.1464152082392, "height": 416.90235690235625, "content": "### A.I. Categorization\n\n1. you need to set up the categories first in wordpress\n\n2. Edit the message prompt and change the categories and category numbers"}, "typeVersion": 1}, {"id": "29354054-8600-4e45-99d0-6f30f779a505", "name": "Sticky Note3", "type": "n8n-nodes-base.stickyNote", "position": [1480, 240], "parameters": {"color": 5, "width": 171.64983164983155, "height": 269.59595959595947, "content": "### Update category"}, "typeVersion": 1}, {"id": "d9fe6289-6b97-4830-80aa-754ac4d4b3e0", "name": "Get All Wordpress Posts", "type": "n8n-nodes-base.wordpress", "position": [900, 320], "parameters": {"options": {}, "operation": "getAll", "returnAll": true}, "credentials": {"wordpressApi": {"id": "lGWPwxTdfPDDbFjj", "name": "Rumjahn.com wordpress"}}, "typeVersion": 1}, {"id": "ed40bf13-8294-4b4e-a8b6-5749989d3420", "name": "OpenAI Chat Model", "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "position": [1080, 540], "parameters": {"options": {}}, "credentials": {"openAiApi": {"id": "XO3iT1iYT5Vod56X", "name": "OpenAi account"}}, "typeVersion": 1}, {"id": "dafeb935-532e-4067-9dfb-7e9a6bbc4e5a", "name": "AI Agent", "type": "@n8n/n8n-nodes-langchain.agent", "position": [1100, 320], "parameters": {"text": "=You are an expert content strategist and taxonomy specialist with extensive experience in blog categorization and content organization.\n\nI will provide you with a blog post's title. Your task is to assign ONE primary category ID from this fixed list:\n\n13 = Content Creation\n14 = Digital Marketing\n15 = AI Tools\n17 = Automation & Integration\n18 = Productivity Tools\n19 = Analytics & Strategy\n\nAnalyze the title and return only the single most relevant category ID number that best represents the main focus of the post. While a post might touch on multiple topics, select the dominant theme that would be most useful for navigation purposes.\n\n{{ $json.title.rendered }}\n\nOutput only the category number", "options": {}, "promptType": "define"}, "typeVersion": 1.7}], "active": false, "pinData": {}, "settings": {"executionOrder": "v1"}, "versionId": "2a753171-425f-4b5a-bd1b-8591ad2d142c", "connections": {"AI Agent": {"main": [[{"node": "Wordpress", "type": "main", "index": 0}]]}, "OpenAI Chat Model": {"ai_languageModel": [[{"node": "AI Agent", "type": "ai_languageModel", "index": 0}]]}, "Get All Wordpress Posts": {"main": [[{"node": "AI Agent", "type": "main", "index": 0}]]}, "When clicking ‘Test workflow’": {"main": [[{"node": "Get All Wordpress Posts", "type": "main", "index": 0}]]}}}