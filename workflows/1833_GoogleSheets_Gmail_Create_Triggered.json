{"id": "dCLvOuZgc8tToQwu", "meta": {"instanceId": "14e4c77104722ab186539dfea5182e419aecc83d85963fe13f6de862c875ebfa", "templateCredsSetupCompleted": true}, "name": "Add new incoming emails to a Google Sheets spreadsheet as a new row.", "tags": [], "nodes": [{"id": "4db1f92f-6425-41c4-8f26-94e13ef5cd1f", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.gmailTrigger", "notes": "<PERSON><PERSON>\n", "position": [-200, -20], "parameters": {"filters": {}, "pollTimes": {"item": [{"mode": "everyMinute"}]}}, "credentials": {"gmailOAuth2": {"id": "", "name": ""}}, "notesInFlow": true, "typeVersion": 1.2}, {"id": "77c70cbd-fca7-4925-9a47-e2c903b8a64e", "name": "Google Sheets", "type": "n8n-nodes-base.googleSheets", "position": [180, -20], "parameters": {"columns": {"value": {"body": "={{ $json.snippet }}", "Subject": "={{ $json.Subject }}", "Sender Email": "={{ $json.From }}"}, "schema": [{"id": "Sender <PERSON><PERSON>", "type": "string", "display": true, "required": false, "displayName": "Sender <PERSON><PERSON>", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Subject", "type": "string", "display": true, "required": false, "displayName": "Subject", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "body", "type": "string", "display": true, "required": false, "displayName": "body", "defaultMatch": false, "canBeUsedToMatch": true}], "mappingMode": "defineBelow", "matchingColumns": [], "attemptToConvertTypes": false, "convertFieldsToString": false}, "options": {}, "operation": "append", "sheetName": {"__rl": true, "mode": "list", "value": "gid=0", "cachedResultUrl": "", "cachedResultName": ""}, "documentId": {"__rl": true, "mode": "list", "value": "1o28BFBtzzsnwN01VTcfRp2BUyAFi9e-91H_b920_gJc", "cachedResultUrl": "", "cachedResultName": ""}}, "credentials": {"googleSheetsOAuth2Api": {"id": "", "name": ""}}, "typeVersion": 4.5}, {"id": "0bc68783-e959-40f7-8cc3-a8800e62029a", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [-260, -80], "parameters": {"color": 2, "width": 660, "height": 260, "content": "### Add new incoming emails to a Google Sheets spreadsheet as a new row.\n"}, "typeVersion": 1}, {"id": "90a94a4d-60fc-40d2-8b1e-1bf01c98d789", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [-260, 200], "parameters": {"color": 2, "width": 660, "content": "## Description :\nThis n8n workflow automates the process of storing email details in a spreadsheet whenever a new email is received. It utilizes the Email Trigger node to detect incoming emails and then extracts the sender, subject, and email content, which are subsequently saved into a spreadsheet (e.g., Google Sheets or an Excel file). This ensures a structured record of emails for further processing, analysis, or reporting.\n\nYou can customize this workflow as per your requirements, such as adding additional columns in the spreadsheet to store more details or modifying it for different use cases, like lead tracking, customer inquiries, or automated email logging. "}, "typeVersion": 1}], "active": false, "pinData": {}, "settings": {"executionOrder": "v1"}, "versionId": "d8ab2b16-b091-455b-ad43-8e117a49e297", "connections": {"Gmail Trigger": {"main": [[{"node": "Google Sheets", "type": "main", "index": 0}]]}}}