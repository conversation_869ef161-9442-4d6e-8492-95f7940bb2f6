{"id": "dYjQS1bJmVSAxNnj", "meta": {"instanceId": "a9f3b18652ddc96459b459de4fa8fa33252fb820a9e5a1593074f3580352864a", "templateCredsSetupCompleted": true}, "name": "BambooHR AI-Powered Company Policies and Benefits Chatbot", "tags": [], "nodes": [{"id": "832e4a1d-320f-4793-be3c-8829776a3ce6", "name": "When clicking ‘Test workflow’", "type": "n8n-nodes-base.manualTrigger", "position": [760, 560], "parameters": {}, "typeVersion": 1}, {"id": "63be0638-d7df-4af8-ba56-555593a6de0c", "name": "Default Data Loader", "type": "@n8n/n8n-nodes-langchain.documentDefaultDataLoader", "position": [2080, 740], "parameters": {"options": {}, "dataType": "binary"}, "typeVersion": 1}, {"id": "ffe33bb2-efd0-4b6e-a146-aaded7c28304", "name": "Embeddings OpenAI", "type": "@n8n/n8n-nodes-langchain.embeddingsOpenAi", "position": [1860, 740], "parameters": {"options": {}}, "credentials": {"openAiApi": {"id": "XXXXXX", "name": "OpenAi account"}}, "typeVersion": 1.1}, {"id": "32de5318-ea5d-4951-b81c-3c96167bc320", "name": "Recursive Character Text Splitter", "type": "@n8n/n8n-nodes-langchain.textSplitterRecursiveCharacterTextSplitter", "position": [2060, 880], "parameters": {"options": {}, "chunkOverlap": 100}, "typeVersion": 1}, {"id": "6306d263-16c1-4a68-9318-c58fea1e3e62", "name": "Window Buffer Memory", "type": "@n8n/n8n-nodes-langchain.memoryBufferWindow", "position": [1000, 1340], "parameters": {}, "typeVersion": 1.2}, {"id": "364cf0ce-524c-4b61-89f3-40b2801bc7e3", "name": "OpenAI Chat Model", "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "position": [840, 1340], "parameters": {"options": {}}, "credentials": {"openAiApi": {"id": "XXXXXX", "name": "OpenAi account"}}, "typeVersion": 1}, {"id": "901163a1-1e66-42ee-bfd0-9ed815a7c83d", "name": "Vector Store Tool", "type": "@n8n/n8n-nodes-langchain.toolVectorStore", "position": [1120, 1380], "parameters": {"name": "company_files", "topK": 5, "description": "Retrieves information from the company handbook, 401k policies, benefits overview, and expense policies available to all employees."}, "typeVersion": 1}, {"id": "b87fa113-6a32-48fc-8e06-049345c66f38", "name": "OpenAI Chat Model1", "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "position": [1220, 1600], "parameters": {"options": {}}, "credentials": {"openAiApi": {"id": "XXXXXX", "name": "OpenAi account"}}, "typeVersion": 1}, {"id": "9dc1a896-c8a5-4d22-b029-14eae0717bd8", "name": "Embeddings OpenAI1", "type": "@n8n/n8n-nodes-langchain.embeddingsOpenAi", "position": [940, 1700], "parameters": {"options": {}}, "credentials": {"openAiApi": {"id": "XXXXXX", "name": "OpenAi account"}}, "typeVersion": 1.1}, {"id": "20cda474-ef6f-48af-b299-04f1fe980d3d", "name": "Employee Lookup Tool", "type": "@n8n/n8n-nodes-langchain.toolWorkflow", "position": [1440, 1360], "parameters": {"name": "employee_lookup_tool", "workflowId": {"__rl": true, "mode": "id", "value": "={{ $workflow.id }}"}, "description": "Call this tool with the full name of an employee to retrieve their details from our HRIS, including their job title, department, and supervisor. If an employee name is not provided, you may call this tool with a department name to retrieve the most senior person in that department. This tool requires an exact match on employee names but can infer the senior-most person for a department query.", "jsonSchemaExample": "{\n\t\"name\": \"The name of an employee or department\"\n}", "specifyInputSchema": true}, "typeVersion": 1.2}, {"id": "********-459b-4a4b-8c57-fd6b31e3d963", "name": "OpenAI Chat Model2", "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "position": [1960, 1500], "parameters": {"options": {}}, "credentials": {"openAiApi": {"id": "XXXXXX", "name": "OpenAi account"}}, "typeVersion": 1}, {"id": "e574d63d-7e38-4d90-9533-64a4ddbe2e36", "name": "OpenAI Chat Model3", "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "position": [2980, 1600], "parameters": {"options": {}}, "credentials": {"openAiApi": {"id": "XXXXXX", "name": "OpenAi account"}}, "typeVersion": 1}, {"id": "04d53430-b8d9-43ff-b2c4-ef0da2d799c0", "name": "OpenAI Chat Model4", "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "position": [3700, 1620], "parameters": {"options": {}}, "credentials": {"openAiApi": {"id": "XXXXXX", "name": "OpenAi account"}}, "typeVersion": 1}, {"id": "9759fe08-3c81-4472-8d62-2c5d26156984", "name": "Auto-fixing Output Parser", "type": "@n8n/n8n-nodes-langchain.outputParserAutofixing", "position": [3880, 1600], "parameters": {}, "typeVersion": 1}, {"id": "d8830fd8-f238-4e5d-8c5f-bf83c9450dbe", "name": "OpenAI Chat Model5", "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "position": [3780, 1700], "parameters": {"options": {}}, "credentials": {"openAiApi": {"id": "XXXXXX", "name": "OpenAi account"}}, "typeVersion": 1}, {"id": "da580308-e4ed-400b-99e2-31baf27b039d", "name": "Structured Output Parser", "type": "@n8n/n8n-nodes-langchain.outputParserStructured", "position": [4080, 1700], "parameters": {"jsonSchemaExample": "{\n\t\"name\": \"The name of an employee\"\n}"}, "typeVersion": 1.2}, {"id": "e81dbe81-5f6b-4b2c-a4bc-afa0136e33ac", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [680, 460], "parameters": {"color": 7, "width": 1695.***********, "height": 582.*************, "content": "## STEP #1: Retrieve company policies and load them into a vector store"}, "typeVersion": 1}, {"id": "629872ed-2f99-424d-96da-feee6df96d3d", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [680, 1080], "parameters": {"color": 4, "width": 873.*************, "height": 780.*************, "content": "## BambooHR AI-Powered HR Benefits and Company Policies Chatbot"}, "typeVersion": 1}, {"id": "8888281b-5701-4c62-b76b-a0b6a80d8463", "name": "Sticky Note2", "type": "n8n-nodes-base.stickyNote", "position": [1580, 1075.*************], "parameters": {"color": 7, "width": 2783.*************, "height": 781.************, "content": "## (Optional) STEP #2: Set up employee lookup tool"}, "typeVersion": 1}, {"id": "********-d081-4c17-8108-d0327709f352", "name": "GET all files", "type": "n8n-nodes-base.bambooHr", "position": [960, 560], "parameters": {"resource": "file", "operation": "getAll", "returnAll": true, "simplifyOutput": false}, "credentials": {"bambooHrApi": {"id": "XXXXXX", "name": "BambooHR account"}}, "typeVersion": 1}, {"id": "939881b1-eb18-4ab7-ac4a-9edcc218356f", "name": "Sticky Note3", "type": "n8n-nodes-base.stickyNote", "position": [920, 720], "parameters": {"color": 5, "width": 177.**************, "height": 99.**************, "content": "Toggle **off** the _simplify_ option to ensure categories are retrieved as well"}, "typeVersion": 1}, {"id": "0907a1d3-97e2-4219-bfbc-524186f6d889", "name": "Filter out files from undesired categories", "type": "n8n-nodes-base.filter", "position": [1160, 560], "parameters": {"options": {}, "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "b85b86cd-0b54-4348-a538-8ff4ae625b9a", "operator": {"name": "filter.operator.equals", "type": "string", "operation": "equals"}, "leftValue": "={{ $json.name }}", "rightValue": "=Company Files"}]}}, "typeVersion": 2.2}, {"id": "********-7cd9-4515-846d-ed6a0f9bbb61", "name": "Split out individual files", "type": "n8n-nodes-base.splitOut", "position": [1360, 560], "parameters": {"options": {}, "fieldToSplitOut": "files"}, "typeVersion": 1}, {"id": "8412af5f-f07f-4a98-a174-e363ba04f902", "name": "Filter out non-pdf files", "type": "n8n-nodes-base.filter", "position": [1560, 560], "parameters": {"options": {}, "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "73cc2cb9-04fa-43e7-a459-de0bf26ffb18", "operator": {"type": "boolean", "operation": "true", "singleValue": true}, "leftValue": "={{ $json.originalFileName.endsWith(\".pdf\") }}", "rightValue": ""}]}}, "typeVersion": 2.2}, {"id": "7e007a29-c902-41d3-ab22-f6a93bc43f7d", "name": "Download file from BambooHR", "type": "n8n-nodes-base.bambooHr", "position": [1760, 560], "parameters": {"fileId": "={{ $json.id }}", "resource": "file", "operation": "download"}, "credentials": {"bambooHrApi": {"id": "XXXXXX", "name": "BambooHR account"}}, "typeVersion": 1}, {"id": "cec7ce3a-77df-4400-8683-fb5cf87004b6", "name": "Supabase Vector Store", "type": "@n8n/n8n-nodes-langchain.vectorStoreSupabase", "position": [1960, 560], "parameters": {"mode": "insert", "options": {"queryName": "match_files"}, "tableName": {"__rl": true, "mode": "list", "value": "company_files", "cachedResultName": "company_files"}}, "credentials": {"supabaseApi": {"id": "XXXXXX", "name": "Supabase account"}}, "typeVersion": 1}, {"id": "5e070dc3-5f6d-44bb-a655-b769aac14890", "name": "Sticky Note4", "type": "n8n-nodes-base.stickyNote", "position": [1600, 1140], "parameters": {"color": 5, "width": 530.*************, "height": 91.**************, "content": "This employee lookup tool gives the AI Benefits and Company Policies chatbot additional superpowers by allowing it to **search for an individual or a department to retrieve contact information from BambooHR**."}, "typeVersion": 1}, {"id": "8f3cd44e-d1e5-4806-9d89-78c8728ea0e4", "name": "Employee initiates a conversation", "type": "@n8n/n8n-nodes-langchain.chatTrigger", "position": [760, 1140], "webhookId": "27ec9df7-5007-4642-81c7-7fcf7e834c43", "parameters": {"options": {}}, "typeVersion": 1.1}, {"id": "3d56dc6a-13e2-404b-ad38-6370b9610f61", "name": "Supabase Vector Store Retrieval", "type": "@n8n/n8n-nodes-langchain.vectorStoreSupabase", "position": [940, 1540], "parameters": {"options": {"queryName": "match_files"}, "tableName": {"__rl": true, "mode": "list", "value": "company_files", "cachedResultName": "company_files"}}, "credentials": {"supabaseApi": {"id": "XXXXXX", "name": "Supabase account"}}, "typeVersion": 1}, {"id": "1e6f5d4a-5897-42b7-bfcf-e69b7880b6c4", "name": "Sticky Note5", "type": "n8n-nodes-base.stickyNote", "position": [680, 1880], "parameters": {"width": 865.************, "height": 281.**************, "content": "### AI Chatbot Operating Guidelines \n- When an employee asks for a contact person, first attempt to find the relevant contact in company_files.  \n- If a contact person is found but their details (e.g., email or phone number) are missing, use the `employee_lookup_tool` to retrieve their contact details.  \n- If no contact person is found:  \n  1. Use the `employee_lookup_tool` with \"HR\" (or another relevant department) to retrieve the most senior person in that department.  \n  2. If no senior contact is found, ask the employee for their name.  \n  3. Use the `employee_lookup_tool` to retrieve their supervisor’s name.  \n  4. Use the `employee_lookup_tool` to retrieve their supervisor’s details.  \n  5. Provide the supervisor's contact information and recommend them as the best next point of contact.  "}, "typeVersion": 1}, {"id": "ba8c82cb-4972-46cc-8594-dfe71149a41c", "name": "AI-Powered HR Benefits and Company Policies Chatbot", "type": "n8n-nodes-base.executeWorkflowTrigger", "position": [1640, 1340], "parameters": {}, "typeVersion": 1}, {"id": "aaf611fd-1779-4826-8f9c-4e9a7a538af0", "name": "Text Classifier", "type": "@n8n/n8n-nodes-langchain.textClassifier", "position": [1840, 1340], "parameters": {"options": {}, "inputText": "={{ $json.query.name }}", "categories": {"categories": [{"category": "person", "description": "This is the name of a person."}, {"category": "department", "description": "This is the name of a department within the company."}]}}, "typeVersion": 1}, {"id": "4a1e0d47-87f8-4301-9aee-2227003a40e6", "name": "GET all employees", "type": "n8n-nodes-base.bambooHr", "position": [2260, 1240], "parameters": {"operation": "getAll", "returnAll": true}, "credentials": {"bambooHrApi": {"id": "XXXXXX", "name": "BambooHR account"}}, "typeVersion": 1}, {"id": "93e1017a-07c6-4b97-be90-659a91fdc065", "name": "Filter out other employees", "type": "n8n-nodes-base.filter", "position": [2460, 1240], "parameters": {"options": {}, "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "e80c892e-21dc-4d6e-8ef6-c2ffaea6d43e", "operator": {"name": "filter.operator.equals", "type": "string", "operation": "equals"}, "leftValue": "={{ $json.displayName }}", "rightValue": "={{ $('AI-Powered HR Benefits and Company Policies Chatbot').item.json.query.name }}"}]}}, "typeVersion": 2.2}, {"id": "c45eec9a-05ca-4b35-b595-42f2251a01ec", "name": "Stringify employee record for response", "type": "n8n-nodes-base.set", "position": [2660, 1240], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "73ae7ef0-339a-4e32-bbc9-c40cefd37757", "name": "response", "type": "string", "value": "={{ $json.toJsonString() }}"}]}}, "typeVersion": 3.4}, {"id": "aa30062a-2476-4fc2-8380-6d2106885ae2", "name": "GET all employees (second path)", "type": "n8n-nodes-base.bambooHr", "position": [2260, 1440], "parameters": {"operation": "getAll", "returnAll": true}, "credentials": {"bambooHrApi": {"id": "XXXXXX", "name": "BambooHR account"}}, "typeVersion": 1}, {"id": "f44cb9ab-00aa-4ebc-bb1a-6ba1da2e2aaa", "name": "Extract departments", "type": "n8n-nodes-base.aggregate", "position": [2460, 1440], "parameters": {"options": {}, "fieldsToAggregate": {"fieldToAggregate": [{"renameField": true, "outputFieldName": "departments", "fieldToAggregate": "department"}]}}, "typeVersion": 1}, {"id": "855a6968-d919-4071-96d8-04cbc4b6ec39", "name": "Ensure uniqueness in department list", "type": "n8n-nodes-base.set", "position": [2660, 1440], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "34f456ff-d2c5-431f-ade3-ace48abd0c6a", "name": "departments", "type": "array", "value": "={{ $json.departments.unique() }}"}, {"id": "cf31288a-65fc-45c6-8b6f-6680020dce09", "name": "query", "type": "string", "value": "={{ $('Text Classifier').item.json.query.name }}"}]}}, "typeVersion": 3.4}, {"id": "0dca5763-33c6-4444-b4e0-f26127bb91d5", "name": "Extract department", "type": "@n8n/n8n-nodes-langchain.informationExtractor", "position": [2860, 1440], "parameters": {"text": "={{ $json.query }}", "options": {}, "attributes": {"attributes": [{"name": "department", "description": "=The department from the following list that would be most applicable:\n{{ $json.departments }}"}]}}, "typeVersion": 1}, {"id": "833b43e8-7ed5-4431-b362-b5d11bb9f787", "name": "Retrieve all employees", "type": "n8n-nodes-base.bambooHr", "position": [3220, 1440], "parameters": {"operation": "getAll", "returnAll": true}, "credentials": {"bambooHrApi": {"id": "XXXXXX", "name": "BambooHR account"}}, "typeVersion": 1}, {"id": "adcaafb5-700f-4e93-a7f4-c393967fb4f0", "name": "Filter out other departments", "type": "n8n-nodes-base.filter", "position": [3420, 1440], "parameters": {"options": {}, "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "a88bf53c-ecfd-49a7-8180-1e8b8eaeb6fd", "operator": {"name": "filter.operator.equals", "type": "string", "operation": "equals"}, "leftValue": "={{ $json.department }}", "rightValue": "={{ $('Extract department').item.json.output.department }}"}]}}, "typeVersion": 2.2}, {"id": "fe928eb9-2b70-4ab9-a5a6-a4c141467ad7", "name": "Extract relevant employee fields", "type": "n8n-nodes-base.aggregate", "position": [3620, 1440], "parameters": {"include": "specifiedFields", "options": {}, "aggregate": "aggregateAllItemData", "fieldsToInclude": "id, displayName, jobTitle, workEmail", "destinationFieldName": "department_employees"}, "typeVersion": 1}, {"id": "0632ae1b-280e-486e-9cdd-c6c9fd2a1b6e", "name": "Identify most senior employee", "type": "@n8n/n8n-nodes-langchain.chainLlm", "position": [3800, 1440], "parameters": {"text": "=Who is the most senior employee from this list:\n{{ $json.department_employees.toJsonString() }}", "promptType": "define", "hasOutputParser": true}, "typeVersion": 1.4}, {"id": "0e6c8d0a-d84f-468b-993b-c5a14d7d458f", "name": "Format name for response", "type": "n8n-nodes-base.set", "position": [4160, 1440], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "2b4412bf-142b-4ba0-a6b2-654e97c263e5", "name": "response", "type": "string", "value": "={{ $json.output.name }}"}]}}, "typeVersion": 3.4}, {"id": "e865d8bf-ab6d-4d23-9d7c-a76f96ba75a1", "name": "HR AI Agent", "type": "@n8n/n8n-nodes-langchain.agent", "position": [1040, 1140], "parameters": {"options": {"systemMessage": "You are a helpful HR assistant accessible by employees at our company.\n\nObjective:  \nAssist employees with questions regarding company policies, documents, and escalation procedures.\n\nTools:  \n1. A vector store database (company_files) containing the company handbook, 401k policy, expense policy, and employee benefits.  \n2. An employee lookup tool (employee_lookup_tool) that retrieves details about an employee when provided with their name. It can also retrieve the most senior person in a department if given a department name.  \n\nGuidelines:  \n- When an employee asks for a contact person, first attempt to find the relevant contact in company_files.  \n- If a contact person is found but their details (e.g., email or phone number) are missing, use the `employee_lookup_tool` to retrieve their contact details.  \n- If no contact person is found:  \n  1. Use the `employee_lookup_tool` with \"HR\" (or another relevant department) to retrieve the most senior person in that department.  \n  2. If no senior contact is found, ask the employee for their name.  \n  3. Use the `employee_lookup_tool` to retrieve their supervisor’s name.  \n  4. Use the `employee_lookup_tool` to retrieve their supervisor’s details.  \n  5. Provide the supervisor's contact information and recommend them as the best next point of contact.  \n"}}, "typeVersion": 1.7}, {"id": "3aa42dcf-a411-4bd8-87b3-9ab9d0043303", "name": "Sticky Note6", "type": "n8n-nodes-base.stickyNote", "position": [1600, 1660], "parameters": {"color": 3, "width": 340.93489445096634, "height": 180.79319430657273, "content": "### GetAll employees from BambooHR\nBambooHR does not offer search by {field} functionality for its `/employees` endpoint, so filtering must be done after data retrieval. This can be inefficient for very large organizations where there may be multiple employees with the same name or simply a large number of employees."}, "typeVersion": 1}, {"id": "3b3b400c-9c7e-4fd0-91f3-1c6bcf05617f", "name": "Sticky Note7", "type": "n8n-nodes-base.stickyNote", "position": [2240, 1140], "parameters": {"color": 5, "width": 542.9452105095002, "height": 89.69037140899545, "content": "### GET singular employee by name path\nThis path may be used multiple times by the HR AI Agent to look up the employee's details, and then to look up their supervisor's details."}, "typeVersion": 1}, {"id": "6ad78a36-e68d-4b0d-b532-ca67bcd0738d", "name": "Sticky Note8", "type": "n8n-nodes-base.stickyNote", "position": [2240, 1620], "parameters": {"color": 5, "width": 542.9452105095002, "height": 121.0648445295759, "content": "### GET senior leader of department path\nThis path would normally only be used when no other contacts can be identified from the company_files. The employee can retrieve the contact details for the most senior leader of a department should they request it."}, "typeVersion": 1}, {"id": "25d1e603-cce0-4cd1-9293-810880c65584", "name": "Sticky Note9", "type": "n8n-nodes-base.stickyNote", "position": [4020, 1320], "parameters": {"color": 5, "width": 300.8019702746294, "height": 97.8161667645835, "content": "### Final node returns employee name\nThe AI Agent can then call the employee lookup path to retrieve details, if requested."}, "typeVersion": 1}, {"id": "e7076eaa-a67e-4b02-9aec-553c405f3bb9", "name": "Sticky Note10", "type": "n8n-nodes-base.stickyNote", "position": [700, 940], "parameters": {"color": 4, "width": 244.3952545193282, "height": 87.34661077350344, "content": "## About the maker\n**[<PERSON> on LinkedIn](https://www.linkedin.com/in/ludwiggerdes)**"}, "typeVersion": 1}], "active": false, "pinData": {"AI-Powered HR Benefits and Company Policies Chatbot": [{"json": {"query": {"name": "HR"}}}]}, "settings": {"executionOrder": "v1"}, "versionId": "b4306b84-994f-4cd0-b40c-33a234f75ef9", "connections": {"GET all files": {"main": [[{"node": "Filter out files from undesired categories", "type": "main", "index": 0}]]}, "Text Classifier": {"main": [[{"node": "GET all employees", "type": "main", "index": 0}], [{"node": "GET all employees (second path)", "type": "main", "index": 0}]]}, "Embeddings OpenAI": {"ai_embedding": [[{"node": "Supabase Vector Store", "type": "ai_embedding", "index": 0}]]}, "GET all employees": {"main": [[{"node": "Filter out other employees", "type": "main", "index": 0}]]}, "OpenAI Chat Model": {"ai_languageModel": [[{"node": "HR AI Agent", "type": "ai_languageModel", "index": 0}]]}, "Vector Store Tool": {"ai_tool": [[{"node": "HR AI Agent", "type": "ai_tool", "index": 0}]]}, "Embeddings OpenAI1": {"ai_embedding": [[{"node": "Supabase Vector Store Retrieval", "type": "ai_embedding", "index": 0}]]}, "Extract department": {"main": [[{"node": "Retrieve all employees", "type": "main", "index": 0}]]}, "OpenAI Chat Model1": {"ai_languageModel": [[{"node": "Vector Store Tool", "type": "ai_languageModel", "index": 0}]]}, "OpenAI Chat Model2": {"ai_languageModel": [[{"node": "Text Classifier", "type": "ai_languageModel", "index": 0}]]}, "OpenAI Chat Model3": {"ai_languageModel": [[{"node": "Extract department", "type": "ai_languageModel", "index": 0}]]}, "OpenAI Chat Model4": {"ai_languageModel": [[{"node": "Identify most senior employee", "type": "ai_languageModel", "index": 0}]]}, "OpenAI Chat Model5": {"ai_languageModel": [[{"node": "Auto-fixing Output Parser", "type": "ai_languageModel", "index": 0}]]}, "Default Data Loader": {"ai_document": [[{"node": "Supabase Vector Store", "type": "ai_document", "index": 0}]]}, "Extract departments": {"main": [[{"node": "Ensure uniqueness in department list", "type": "main", "index": 0}]]}, "Employee Lookup Tool": {"ai_tool": [[{"node": "HR AI Agent", "type": "ai_tool", "index": 0}]]}, "Window Buffer Memory": {"ai_memory": [[{"node": "HR AI Agent", "type": "ai_memory", "index": 0}]]}, "Retrieve all employees": {"main": [[{"node": "Filter out other departments", "type": "main", "index": 0}]]}, "Filter out non-pdf files": {"main": [[{"node": "Download file from BambooHR", "type": "main", "index": 0}]]}, "Structured Output Parser": {"ai_outputParser": [[{"node": "Auto-fixing Output Parser", "type": "ai_outputParser", "index": 0}]]}, "Auto-fixing Output Parser": {"ai_outputParser": [[{"node": "Identify most senior employee", "type": "ai_outputParser", "index": 0}]]}, "Filter out other employees": {"main": [[{"node": "Stringify employee record for response", "type": "main", "index": 0}]]}, "Split out individual files": {"main": [[{"node": "Filter out non-pdf files", "type": "main", "index": 0}]]}, "Download file from BambooHR": {"main": [[{"node": "Supabase Vector Store", "type": "main", "index": 0}]]}, "Filter out other departments": {"main": [[{"node": "Extract relevant employee fields", "type": "main", "index": 0}]]}, "Identify most senior employee": {"main": [[{"node": "Format name for response", "type": "main", "index": 0}]]}, "GET all employees (second path)": {"main": [[{"node": "Extract departments", "type": "main", "index": 0}]]}, "Supabase Vector Store Retrieval": {"ai_vectorStore": [[{"node": "Vector Store Tool", "type": "ai_vectorStore", "index": 0}]]}, "Extract relevant employee fields": {"main": [[{"node": "Identify most senior employee", "type": "main", "index": 0}]]}, "Employee initiates a conversation": {"main": [[{"node": "HR AI Agent", "type": "main", "index": 0}]]}, "Recursive Character Text Splitter": {"ai_textSplitter": [[{"node": "Default Data Loader", "type": "ai_textSplitter", "index": 0}]]}, "When clicking ‘Test workflow’": {"main": [[{"node": "GET all files", "type": "main", "index": 0}]]}, "Ensure uniqueness in department list": {"main": [[{"node": "Extract department", "type": "main", "index": 0}]]}, "Filter out files from undesired categories": {"main": [[{"node": "Split out individual files", "type": "main", "index": 0}]]}, "AI-Powered HR Benefits and Company Policies Chatbot": {"main": [[{"node": "Text Classifier", "type": "main", "index": 0}]]}}}