{"meta": {"instanceId": "12345678-1234-1234-1234-123456789012"}, "name": "Weather to Telegram", "nodes": [{"parameters": {"triggerTimes": [{"mode": "everyDay", "hour": 7, "minute": 0}]}, "id": "12345678-1234-1234-1234-123456789001", "name": "Daily Trigger", "type": "n8n-nodes-base.cron", "typeVersion": 1, "position": [200, 300]}, {"parameters": {"url": "https://api.openweathermap.org/data/2.5/weather?q=Chennai&appid=YOUR_API_KEY&units=metric", "options": {}}, "id": "12345678-1234-1234-1234-123456789002", "name": "Get Weather", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [400, 300]}, {"parameters": {"jsCode": "const temp = $input.first().json.main.temp;\nconst desc = $input.first().json.weather[0].description;\n\nreturn {\n  message: `🌤️ Good morning!\\nWeather in Chennai:\\nTemp: ${temp}°C\\nCondition: ${desc}`\n};"}, "id": "12345678-1234-1234-1234-123456789003", "name": "Format Message", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [600, 300]}, {"parameters": {"chatId": "YOUR_CHAT_ID", "text": "={{ $json.message }}"}, "id": "12345678-1234-1234-1234-123456789004", "name": "Send Telegram", "type": "n8n-nodes-base.telegram", "typeVersion": 1.2, "position": [800, 300], "credentials": {"telegramApi": {"id": "1", "name": "My Telegram Bot"}}}], "pinData": {}, "connections": {"Daily Trigger": {"main": [[{"node": "Get Weather", "type": "main", "index": 0}]]}, "Get Weather": {"main": [[{"node": "Format Message", "type": "main", "index": 0}]]}, "Format Message": {"main": [[{"node": "Send Telegram", "type": "main", "index": 0}]]}}, "active": false, "settings": {"executionOrder": "v1"}, "versionId": "12345678-1234-1234-1234-123456789099", "id": "1", "tags": []}