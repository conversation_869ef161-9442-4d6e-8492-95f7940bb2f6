{"name": "Weather to Telegram", "nodes": [{"parameters": {"triggerTimes": [{"mode": "everyDay", "hour": 7, "minute": 0}]}, "id": "cron", "name": "Daily Trigger", "type": "n8n-nodes-base.cron", "typeVersion": 1, "position": [200, 300]}, {"parameters": {"url": "https://api.openweathermap.org/data/2.5/weather?q=Chennai&appid=YOUR_API_KEY&units=metric", "responseFormat": "json"}, "id": "httpRequest", "name": "Get Weather", "type": "n8n-nodes-base.httpRequest", "typeVersion": 2, "position": [400, 300]}, {"parameters": {"functionCode": "const temp = $json.main.temp;\nconst desc = $json.weather[0].description;\n\nreturn [{\n  json: {\n    message: `🌤️ Good morning!\\nWeather in Chennai:\\nTemp: ${temp}°C\\nCondition: ${desc}`\n  }\n}];"}, "id": "formatMsg", "name": "Format Message", "type": "n8n-nodes-base.function", "typeVersion": 1, "position": [600, 300]}, {"parameters": {"chatId": "YOUR_CHAT_ID", "text": "={{$json[\"message\"]}}"}, "id": "telegram", "name": "Send Telegram", "type": "n8n-nodes-base.telegram", "typeVersion": 1, "position": [800, 300], "credentials": {"telegramApi": {"id": "1", "name": "My Telegram Bot"}}}], "connections": {"Daily Trigger": {"main": [[{"node": "Get Weather", "type": "main", "index": 0}]]}, "Get Weather": {"main": [[{"node": "Format Message", "type": "main", "index": 0}]]}, "Format Message": {"main": [[{"node": "Send Telegram", "type": "main", "index": 0}]]}}, "active": false, "settings": {}, "version": 1}